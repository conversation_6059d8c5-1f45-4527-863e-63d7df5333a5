import requests
import json
import csv
import os
from datetime import datetime


def get_kuaishou_index_data(keywords, cookie):
    """获取快手指数数据"""
    url = "https://index.e.kuaishou.com/rest/index/detail/keyword-valid"

    headers = {
        "accept": "application/json, text/plain, */*",
        "accept-language": "zh-CN,zh;q=0.9",
        "content-type": "application/json",
        "ktrace-str": "3|My43ODY4Njk4ODQ1NzU3MDg4LjU0MjY0MzM2LjE3NTQzNzU4MTYzMjguMTA3OQ==|My43ODY4Njk4ODQ1NzU3MDg4LjQzODE3NTgxLjE3NTQzNzU4MTYzMjguMTA3OA==|0|ad-ks-index-kfx|ad|true|src:Js,seqn:8401,rsi:17c888a0-acdf-4cbc-b35f-f566cbf93465,path:/zhishu/keywordAnalysis,rpi:74b7612e00",
        "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"",
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": "\"Windows\"",
        "sec-fetch-dest": "empty",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "same-origin",
        "x-requested-with": "XMLHttpRequest",
        "cookie": cookie,
        # "Referer": "https://index.e.kuaishou.com/zhishu/keywordAnalysis?keywordId=219779,673614,423349,21248,695520&keyword=%E4%BA%8C%E6%AC%A1%E5%85%83%2C%E8%83%A1%E6%AD%8C%2C%E8%83%A1%E6%AD%8C%E7%94%B7%E7%A5%9E%2C%E8%83%A1%E6%AD%8C%E7%BB%93%E5%A9%9A%2C%E5%BD%B1%E8%A7%86"
    }

    payload = {
        "keywords": keywords
    }

    try:
        print(f"正在查询关键词: {', '.join(keywords)}")

        # 发送API请求
        response = requests.post(
            url,
            data=json.dumps(payload),
            headers=headers,
            timeout=10
        )
        response.raise_for_status()

        # 解析响应数据
        response_data = response.json()
        print(f"API响应状态: {response.status_code}")

        # 直接返回data字段，供下一步请求使用
        if response_data and response_data.get('success') and response_data.get('data'):
            return response_data['data']
        else:
            print(f"API返回错误: {response_data}")
            return None

    except requests.exceptions.RequestException as e:
        print(f"请求失败: {str(e)}")
        return None
    except json.JSONDecodeError:
        print("JSON解析失败")
        return None


def get_kuaishou_detail_data(keyword_list, start_date, end_date, area_list, cookie):
    """获取快手指数详细数据"""
    url = "https://index.e.kuaishou.com/rest/index/detail/keyword-index"

    headers = {
        "accept": "application/json, text/plain, */*",
        "accept-language": "zh-CN,zh;q=0.9",
        "content-type": "application/json",
        "ktrace-str": "3|My43ODY4Njk4ODQ1NzU3MDg4Ljk2ODk0Njk3LjE3NTQzNzY4MzExMDcuMTA4NQ==|My43ODY4Njk4ODQ1NzU3MDg4LjQ1ODc3NTI4LjE3NTQzNzY4MzExMDcuMTA4NA==|0|ad-ks-index-kfx|ad|true|src:Js,seqn:8401,rsi:17c888a0-acdf-4cbc-b35f-f566cbf93465,path:/zhishu/keywordAnalysis,rpi:74b7612e00",
        "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"",
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": "\"Windows\"",
        "sec-fetch-dest": "empty",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "same-origin",
        "x-requested-with": "XMLHttpRequest",
        "cookie": cookie,
        "Referer": "https://index.e.kuaishou.com/zhishu/keywordAnalysis?keywordId=219779,673614,423349,21248,695520&keyword=%E4%BA%8C%E6%AC%A1%E5%85%83%2C%E8%83%A1%E6%AD%8C%2C%E8%83%A1%E6%AD%8C%E7%94%B7%E7%A5%9E%2C%E8%83%A1%E6%AD%8C%E7%BB%93%E5%A9%9A%2C%E5%BD%B1%E8%A7%86"
    }

    payload = {
        "startDate": start_date,
        "endDate": end_date,
        "keywordList": keyword_list,
        "areaList": area_list
    }

    try:
        print(f"正在获取详细指数数据...")
        print(f"时间范围: {start_date} - {end_date}")
        print(f"关键词数量: {len(keyword_list)}")
        print(f"地区: {', '.join(area_list)}")

        # 发送API请求
        response = requests.post(
            url,
            data=json.dumps(payload),
            headers=headers,
            timeout=10
        )
        response.raise_for_status()

        # 解析响应数据
        response_data = response.json()
        print(f"API响应状态: {response.status_code}")

        # 直接返回data字段
        if response_data and response_data.get('success') and response_data.get('data'):
            return response_data['data']
        else:
            print(f"API返回错误: {response_data}")
            return None

    except requests.exceptions.RequestException as e:
        print(f"请求失败: {str(e)}")
        return None
    except json.JSONDecodeError:
        print("JSON解析失败")
        return None


def format_number(value):
    """格式化数字，将科学计数法转换为普通数字格式"""
    if value == 'N/A' or value is None:
        return 'N/A'

    try:
        # 将字符串转换为浮点数（自动处理科学计数法）
        num = float(value)

        # 如果是整数，显示为整数；否则保留2位小数
        if num == int(num):
            return str(int(num))
        else:
            return f"{num:.2f}"
    except (ValueError, TypeError):
        return str(value)


def generate_keyword_tables(detail_data, area, time_range, output_folder):
    """为每个关键词生成表格文件"""
    # 确保输出文件夹存在
    if not os.path.exists(output_folder):
        os.makedirs(output_folder)
        print(f"已创建文件夹: {output_folder}")

    for keyword_data in detail_data:
        keyword_label = keyword_data.get('keywordLabel', 'Unknown')
        keyword_id = keyword_data.get('keywordId', 'Unknown')

        # 生成表格文件名：关键词-地区-时间范围
        filename = f"{keyword_label}-{area}-{time_range}.csv"
        filepath = os.path.join(output_folder, filename)

        with open(filepath, 'w', newline='', encoding='utf-8-sig') as csvfile:
            csv_writer = csv.writer(csvfile)

            # 写入表头
            csv_writer.writerow(['关键词', '地区', '时间范围', '搜索指数', '综合指数', '内容指数', '互动指数'])

            # 获取各项指数数据
            search_list = keyword_data.get('searchList', [])
            comprehensive_list = keyword_data.get('comprehensiveList', [])
            content_list = keyword_data.get('contentList', [])
            interact_list = keyword_data.get('interactList', [])

            # 按datetime时间进行对应，创建时间到数据的映射
            search_dict = {item['datetime']: item['value'] for item in search_list}
            comprehensive_dict = {item['datetime']: item['value'] for item in comprehensive_list}
            content_dict = {item['datetime']: item['value'] for item in content_list}
            interact_dict = {item['datetime']: item['value'] for item in interact_list}

            # 获取所有时间点（取并集）
            all_dates = set()
            all_dates.update(search_dict.keys())
            all_dates.update(comprehensive_dict.keys())
            all_dates.update(content_dict.keys())
            all_dates.update(interact_dict.keys())

            # 按时间排序
            sorted_dates = sorted(all_dates)

            # 写入数据行
            for date in sorted_dates:
                # 格式化日期显示
                formatted_date = f"{date[:4]}-{date[4:6]}-{date[6:]}"

                # 获取并格式化数值
                search_value = format_number(search_dict.get(date, 'N/A'))
                comprehensive_value = format_number(comprehensive_dict.get(date, 'N/A'))
                content_value = format_number(content_dict.get(date, 'N/A'))
                interact_value = format_number(interact_dict.get(date, 'N/A'))

                csv_writer.writerow([
                    keyword_label,
                    area,
                    formatted_date,
                    search_value,
                    comprehensive_value,
                    content_value,
                    interact_value
                ])

        print(f"已生成表格: {filepath}")

    return len(detail_data)

if __name__ == "__main__":
    # Cookie配置 - 需要从浏览器获取最新的cookie
    cookie = "kGateway-identity=kGateway-d65212a1-1264167144; weblogger_did=web_3886670404BAFCB1; did=web_2cd75a8fc11523de96323905f76c14fc592d; userId=43424604; kuaishou.ad.ksIndex_st=ChZrdWFpc2hvdS5hZC5rc0luZGV4LnN0ErABgx1CHOd_PGe4nKOyK_ICzoXjm0XZ08IeZaIT2jjYlsi-HSgvTppoZH2eDzcVO8g5m9VK062Rcq0cfF96mq0NIzZT6Ja4D2tlN37p2EKA3CnhClp7Mphk6wymBQWbFO6ZECJftiego3jD7sVgFWaFm3QnR1WVXAX3cqAGU-QfzBhDan9fsnfWiHyJKbDc1L3cYZvq5wOzky1DtrB-vAfWY5tEi-5oZmaaqE57bkc9aBoaEp09kqfaxOaT_00X8MODjpRE1SIgdN0CMgs5SWnc3oKwBjT1yXsNuM8fIzD2S04nVLUHOzMoBTAB; kuaishou.ad.ksIndex_ph=46476f8ae56fc5752529760b67cabeddacc5; Hm_lvt_55b6f6890a6937842cef785d95ea99d7=**********,**********; Hm_lpvt_55b6f6890a6937842cef785d95ea99d7=**********; HMACCOUNT=4B4FEA3A0636DF36; Hm_lvt_ed0a6497a1fdcdb3cdca291a7692408d=**********,**********; Hm_lpvt_ed0a6497a1fdcdb3cdca291a7692408d=**********; Hm_lvt_2f06440050c04107e4de7a8003748f65=**********,**********; Hm_lpvt_2f06440050c04107e4de7a8003748f65=**********"

    # 关键词配置
    keywords = ["二次元", "胡歌", "胡歌男神", "胡歌结婚", "影视"]

    # 详细数据查询配置
    start_date = "********"  # 开始日期
    end_date = "********"    # 结束日期
    area_list = ["全国"]     # 地区列表

    # 输出文件夹配置
    output_folder = "快手指数"

    print("=== 第一步：获取关键词ID ===")
    keyword_data = get_kuaishou_index_data(keywords, cookie)

    if keyword_data:
        print(f"成功获取数据，共 {len(keyword_data)} 个关键词:")
        for item in keyword_data:
            print(f"  关键词ID: {item.get('keywordId')}, 关键词: {item.get('keywordLabel')}")

        # 提取关键词ID列表用于详细数据查询
        keyword_ids = [item.get('keywordId') for item in keyword_data]

        print(f"\n=== 第二步：获取详细指数数据 ===")
        detail_data = get_kuaishou_detail_data(keyword_ids, start_date, end_date, area_list, cookie)

        if detail_data:
            print(f"成功获取详细数据，共 {len(detail_data)} 个关键词的指数数据")

            # 生成时间范围字符串用于文件名
            time_range = f"{start_date[:4]}{start_date[4:6]}{start_date[6:]}-{end_date[:4]}{end_date[4:6]}{end_date[6:]}"
            area = area_list[0] if area_list else "全国"

            print(f"\n=== 第三步：生成表格文件 ===")
            table_count = generate_keyword_tables(detail_data, area, time_range, output_folder)
            print(f"成功生成 {table_count} 个关键词表格文件")
            print(f"文件保存在: {os.path.abspath(output_folder)} 文件夹中")

        else:
            print("获取详细数据失败")

    else:
        print("未能获取关键词数据")
        print("\n提示:")
        print("1. 请检查cookie是否有效（可能需要重新从浏览器获取）")
        print("2. 请确认网络连接正常")
        print("3. 请检查关键词是否正确")